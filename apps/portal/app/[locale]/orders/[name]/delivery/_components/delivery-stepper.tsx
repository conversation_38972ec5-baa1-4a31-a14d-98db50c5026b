import type { GetOrdersQuery } from '@/types/admin.generated';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import {
  Stepper,
  StepperIndicator,
  StepperItem,
  StepperTitle,
  StepperTrigger,
} from '@repo/design-system/components/ui/stepper';
import { cn } from '@repo/design-system/lib/utils';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import { useEffect, useState } from 'react';

export function DeliveryStepper({
  order,
  dictionary,
}: {
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}) {
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [isCheckingDelivery, setIsCheckingDelivery] = useState(false);

  // get casefinite order status and check delivery status
  useEffect(() => {
    const checkLogisticStatus = async () => {
      setIsCheckingStatus(true);

      try {
        const response = await fetch('/api/check-logistic-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            orderName: order.name.replace('#', ''),
            email: order.email,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to check logistic status');
        }

        const data = (await response.json()) as {
          logisticStatus: number;
        };

        let step = 1;
        if (data.logisticStatus >= 4) {
          step = 2;
        }
        if (data.logisticStatus >= 5) {
          step = 3;
        }
        if (data.logisticStatus >= 7) {
          step = 4;
        }

        // If we're at step 3 (in transit), check for delivery completion
        if (step === 3) {
          await checkDeliveryStatus(step);
        } else {
          setCurrentStep(step);
        }
      } catch (error) {
        log.error('Error checking logistic status:', { error });
        setCurrentStep(1); // Default to first step on error
      } finally {
        setIsCheckingStatus(false);
      }
    };

    const checkDeliveryStatus = async (currentLogisticStep: number) => {
      setIsCheckingDelivery(true);

      try {
        // Get tracking URL from fulfillments
        const trackingUrl = order.fulfillments?.find(
          (f) => f.trackingInfo && f.trackingInfo.length > 0
        )?.trackingInfo?.[0]?.url;

        if (!trackingUrl) {
          setCurrentStep(currentLogisticStep);
          return;
        }

        const response = await fetch('/api/check-delivery-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            trackingUrl,
            keyword: '配達完了',
          }),
        });

        if (!response.ok) {
          log.warn('Failed to check delivery status, using logistic step');
          setCurrentStep(currentLogisticStep);
          return;
        }

        const data = (await response.json()) as {
          success: boolean;
        };

        // If delivered, set to step 4, otherwise use the logistic step
        setCurrentStep(data.success ? 4 : currentLogisticStep);
      } catch (error) {
        log.error('Error checking delivery status:', { error });
        setCurrentStep(currentLogisticStep);
      } finally {
        setIsCheckingDelivery(false);
      }
    };

    checkLogisticStatus();
  }, [order]);

  if (currentStep === 0 || isCheckingStatus || isCheckingDelivery) {
    return (
      <div className="space-y-1">
        <div className="flex w-full justify-between space-x-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
        </div>
        <div className="flex w-full justify-between space-x-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
        </div>
      </div>
    );
  }

  return (
    <Stepper defaultValue={currentStep} orientation="horizontal">
      <StepperItem step={1} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">1</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 1 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery.processing}
        </StepperTitle>
      </StepperItem>
      <StepperItem step={2} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">2</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 2 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery.preparing_for_delivery}
        </StepperTitle>
      </StepperItem>
      <StepperItem step={3} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">3</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 3 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery.in_transit}
        </StepperTitle>
      </StepperItem>
      <StepperItem step={4} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">4</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 4 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery.delivered}
        </StepperTitle>
      </StepperItem>
    </Stepper>
  );
}
