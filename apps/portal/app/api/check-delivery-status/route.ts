import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const { trackingUrl, keyword }: { trackingUrl: string; keyword: string } =
    await request.json();

  log.info(`Checking delivery status for tracking URL: ${trackingUrl}`);

  if (!keyword) {
    return NextResponse.json({ error: 'Keyword is required' }, { status: 400 });
  }

  if (!trackingUrl) {
    return NextResponse.json(
      { error: 'Tracking URL is required' },
      { status: 400 }
    );
  }

  try {
    const response = await fetch('http://localhost:4445/api/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Api-Key': '04-1UWfqjsfHW0aRwjcCgbyVN9687aV6',
      },
      body: JSON.stringify({
        url: trackingUrl,
        keyword,
      }),
    });

    const result = await response.json();

    log.info(result.data.keyword_found);

    return NextResponse.json(
      { success: result.data.keyword_found },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error checking delivery status:', { error });
    return NextResponse.json(
      {
        isDelivered: false,
        error: 'Failed to check delivery status',
      },
      { status: 200 }
    );
  }
}
