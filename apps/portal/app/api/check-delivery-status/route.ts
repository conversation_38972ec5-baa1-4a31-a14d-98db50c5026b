import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { trackingUrl }: { trackingUrl: string } = await request.json();

    log.info(`Checking delivery status for tracking URL: ${trackingUrl}`);

    if (!trackingUrl) {
      return NextResponse.json(
        { error: 'Tracking URL is required' },
        { status: 400 }
      );
    }

    try {
      // Fetch the tracking page content
      const response = await fetch(trackingUrl, {
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        // Add timeout to prevent hanging requests
        signal: AbortSignal.timeout(10000), // 10 seconds timeout
      });

      if (!response.ok) {
        log.warn(
          `Failed to fetch tracking URL: ${response.status} ${response.statusText}`
        );
        return NextResponse.json(
          {
            isDelivered: false,
            error: 'Failed to fetch tracking information',
          },
          { status: 200 }
        );
      }

      const html = await response.text();

      // Check if the page contains the delivery completion text
      const isDelivered = html.includes('配達完了');

      log.info(
        `Delivery status check result for ${trackingUrl}: ${isDelivered ? 'delivered' : 'not delivered'}`
      );

      return NextResponse.json(
        {
          isDelivered,
        },
        { status: 200 }
      );
    } catch (fetchError) {
      log.error('Error fetching tracking URL:', {
        error: fetchError,
        trackingUrl,
      });
      return NextResponse.json(
        {
          isDelivered: false,
          error: 'Failed to check delivery status',
        },
        { status: 200 }
      );
    }
  } catch (error) {
    log.error('Error checking delivery status:', { error });
    return NextResponse.json(
      {
        error: 'An error occurred while checking delivery status',
      },
      { status: 500 }
    );
  }
}
